/**
 * 统一关键帧工具函数（新架构版本）
 * 适配UnifiedTimelineItemData和统一类型系统，实现统一关键帧系统的核心逻辑
 */

import type { UnifiedTimelineItemData, TransformData } from '../timelineitem/TimelineItemData'

// ==================== 关键帧数据接口（新架构） ====================

/**
 * 统一关键帧接口（适配新架构）
 */
export interface UnifiedKeyframe {
  framePosition: number // 相对于clip开始的帧数
  properties: UnifiedKeyframeProperties
}

/**
 * 统一关键帧属性接口（适配新架构）
 */
export interface UnifiedKeyframeProperties {
  // 变换属性
  x?: number
  y?: number
  width?: number
  height?: number
  rotation?: number
  opacity?: number
  zIndex?: number
  // 音频属性（如果适用）
  volume?: number
}

/**
 * 统一动画配置接口（适配新架构）
 */
export interface UnifiedAnimationConfig {
  keyframes: UnifiedKeyframe[]
  isEnabled: boolean
  easing: 'linear' | 'ease-in' | 'ease-out' | 'ease-in-out'
}

// ==================== 关键帧位置转换工具函数 ====================

/**
 * 将绝对帧数转换为相对于clip开始的帧数
 * @param absoluteFrame 绝对帧数（相对于整个项目时间轴）
 * @param timeRange clip的时间范围
 * @returns 相对于clip开始的帧数
 */
export function absoluteFrameToRelativeFrame(absoluteFrame: number, timeRange: any): number {
  const clipStartFrame = timeRange.timelineStartTime
  const relativeFrame = absoluteFrame - clipStartFrame

  // 确保相对帧数不小于0
  return Math.max(0, relativeFrame)
}

/**
 * 将相对于clip开始的帧数转换为绝对帧数
 * @param relativeFrame 相对于clip开始的帧数
 * @param timeRange clip的时间范围
 * @returns 绝对帧数（相对于整个项目时间轴）
 */
export function relativeFrameToAbsoluteFrame(relativeFrame: number, timeRange: any): number {
  const clipStartFrame = timeRange.timelineStartTime
  return clipStartFrame + relativeFrame
}

// ==================== 关键帧基础操作 ====================

/**
 * 初始化动画配置（新架构版本）
 * 如果UnifiedTimelineItemData没有动画配置，则创建一个空的配置
 */
export function initializeUnifiedAnimation(item: UnifiedTimelineItemData): void {
  // 在新架构中，动画配置可能存储在不同的位置
  // 这里需要根据新架构的具体实现来调整
  if (!(item.config as any).animation) {
    (item.config as any).animation = {
      keyframes: [],
      isEnabled: false,
      easing: 'linear',
    } as UnifiedAnimationConfig
  }
}

/**
 * 创建包含所有属性的关键帧（新架构版本）
 * @param item 统一时间轴项目
 * @param absoluteFrame 绝对帧数（相对于整个项目时间轴）
 * @returns 新创建的关键帧
 */
export function createUnifiedKeyframe(item: UnifiedTimelineItemData, absoluteFrame: number): UnifiedKeyframe {
  const relativeFrame = absoluteFrameToRelativeFrame(absoluteFrame, item.timeRange)
  const transform = item.config.transform

  // 根据媒体类型创建不同的关键帧属性
  const properties: UnifiedKeyframeProperties = {}

  if (transform) {
    properties.x = transform.x
    properties.y = transform.y
    properties.width = transform.width
    properties.height = transform.height
    properties.rotation = transform.rotation
    properties.opacity = transform.opacity
    properties.zIndex = transform.zIndex
  }

  // 如果是视频或音频类型，添加音量属性
  if (item.mediaType === 'video' || item.mediaType === 'audio') {
    const audioConfig = (item.config as any).audioConfig
    if (audioConfig) {
      properties.volume = audioConfig.volume || 1
    }
  }

  return {
    framePosition: relativeFrame,
    properties,
  }
}

/**
 * 启用动画（新架构版本）
 */
export function enableUnifiedAnimation(item: UnifiedTimelineItemData): void {
  initializeUnifiedAnimation(item)
  const animation = (item.config as any).animation as UnifiedAnimationConfig
  if (animation) {
    animation.isEnabled = true
  }
}

/**
 * 禁用动画（新架构版本）
 */
export function disableUnifiedAnimation(item: UnifiedTimelineItemData): void {
  const animation = (item.config as any).animation as UnifiedAnimationConfig
  if (animation) {
    animation.isEnabled = false
    animation.keyframes = []
  }
}

/**
 * 按帧位置排序关键帧（新架构版本）
 */
export function sortUnifiedKeyframes(item: UnifiedTimelineItemData): void {
  const animation = (item.config as any).animation as UnifiedAnimationConfig
  if (!animation) return

  animation.keyframes.sort((a, b) => a.framePosition - b.framePosition)
}

/**
 * 删除指定帧的关键帧（新架构版本）
 */
export function removeUnifiedKeyframeAtFrame(item: UnifiedTimelineItemData, absoluteFrame: number): boolean {
  const animation = (item.config as any).animation as UnifiedAnimationConfig
  if (!animation) return false

  const relativeFrame = absoluteFrameToRelativeFrame(absoluteFrame, item.timeRange)
  const initialLength = animation.keyframes.length

  // 删除匹配的关键帧（允许小的误差）
  animation.keyframes = animation.keyframes.filter(
    (kf) => Math.abs(kf.framePosition - relativeFrame) > 0.5
  )

  return animation.keyframes.length < initialLength
}

/**
 * 检查指定帧是否有关键帧（新架构版本）
 */
export function hasUnifiedKeyframeAtFrame(item: UnifiedTimelineItemData, absoluteFrame: number): boolean {
  const animation = (item.config as any).animation as UnifiedAnimationConfig
  if (!animation) return false

  const relativeFrame = absoluteFrameToRelativeFrame(absoluteFrame, item.timeRange)
  return animation.keyframes.some((kf) => Math.abs(kf.framePosition - relativeFrame) <= 0.5)
}

/**
 * 获取关键帧按钮状态（新架构版本）
 */
export function getUnifiedKeyframeButtonState(
  item: UnifiedTimelineItemData,
  currentFrame: number
): 'none' | 'on-keyframe' | 'between-keyframes' {
  const animation = (item.config as any).animation as UnifiedAnimationConfig
  
  if (!animation || !animation.isEnabled) {
    return 'none'
  }

  if (hasUnifiedKeyframeAtFrame(item, currentFrame)) {
    return 'on-keyframe'
  }

  return 'between-keyframes'
}

/**
 * 统一关键帧切换逻辑（新架构版本）
 * 根据当前状态执行相应的操作
 */
export function toggleUnifiedKeyframe(item: UnifiedTimelineItemData, currentFrame: number): void {
  if (!item) {
    console.error('🎬 [Unified Keyframe] Invalid timeline item')
    return
  }

  const buttonState = getUnifiedKeyframeButtonState(item, currentFrame)

  switch (buttonState) {
    case 'none':
      handleClick_NoAnimation(item, currentFrame)
      break
    case 'on-keyframe':
      handleClick_OnKeyframe(item, currentFrame)
      break
    case 'between-keyframes':
      handleClick_BetweenKeyframes(item, currentFrame)
      break
  }
}

/**
 * 处理关键帧按钮点击 - 状态1：黑色（无动画）→ 蓝色
 */
function handleClick_NoAnimation(item: UnifiedTimelineItemData, currentFrame: number): void {
  // 1. 启用动画
  enableUnifiedAnimation(item)

  // 2. 在当前帧创建包含所有属性的关键帧
  const keyframe = createUnifiedKeyframe(item, currentFrame)
  const animation = (item.config as any).animation as UnifiedAnimationConfig
  animation.keyframes.push(keyframe)

  // 3. 排序关键帧
  sortUnifiedKeyframes(item)

  console.log('🎬 [Unified Keyframe] Created initial keyframe:', {
    itemId: item.id,
    frame: currentFrame,
    keyframe,
  })
}

/**
 * 处理关键帧按钮点击 - 状态2：蓝色（在关键帧）→ 金色或黑色
 */
function handleClick_OnKeyframe(item: UnifiedTimelineItemData, currentFrame: number): void {
  // 1. 删除当前帧的关键帧
  removeUnifiedKeyframeAtFrame(item, currentFrame)

  const animation = (item.config as any).animation as UnifiedAnimationConfig
  
  // 2. 检查是否还有其他关键帧
  if (animation.keyframes.length > 0) {
    // 还有其他关键帧：蓝色 → 金色
    console.log('🎬 [Unified Keyframe] Removed keyframe, animation continues:', {
      itemId: item.id,
      frame: currentFrame,
      remainingKeyframes: animation.keyframes.length,
    })
  } else {
    // 没有其他关键帧：蓝色 → 黑色
    disableUnifiedAnimation(item)
    console.log('🎬 [Unified Keyframe] Removed last keyframe, disabled animation:', {
      itemId: item.id,
      frame: currentFrame,
    })
  }
}

/**
 * 处理关键帧按钮点击 - 状态3：金色（不在关键帧）→ 蓝色
 */
function handleClick_BetweenKeyframes(item: UnifiedTimelineItemData, currentFrame: number): void {
  // 1. 在当前帧创建包含所有属性的关键帧
  const keyframe = createUnifiedKeyframe(item, currentFrame)
  const animation = (item.config as any).animation as UnifiedAnimationConfig
  animation.keyframes.push(keyframe)

  // 2. 排序关键帧
  sortUnifiedKeyframes(item)

  console.log('🎬 [Unified Keyframe] Created new keyframe:', {
    itemId: item.id,
    frame: currentFrame,
    keyframe,
  })
}

/**
 * 清除所有关键帧（新架构版本）
 */
export function clearAllUnifiedKeyframes(item: UnifiedTimelineItemData): void {
  const animation = (item.config as any).animation as UnifiedAnimationConfig
  if (!animation) return

  animation.keyframes = []
  animation.isEnabled = false

  console.log('🎬 [Unified Keyframe] Cleared all keyframes:', {
    itemId: item.id,
  })
}
