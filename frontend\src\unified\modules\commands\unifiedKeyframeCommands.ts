/**
 * 统一关键帧操作命令类
 * 为新架构的关键帧系统提供撤销/重做支持
 * 适配UnifiedTimelineItemData和统一类型系统
 */

import type { SimpleCommand } from '../../../types'
import type { UnifiedTimelineItemData } from '../../timelineitem/TimelineItemData'
import type { UnifiedMediaItemData, MediaType } from '../../mediaitem/types'
import { generateCommandId } from '../../../utils/idGenerator'

// ==================== 关键帧数据快照接口 ====================

/**
 * 统一关键帧状态快照
 * 用于保存和恢复关键帧的完整状态
 * 适配新架构的统一类型系统
 */
interface UnifiedKeyframeSnapshot {
  /** 动画配置的完整快照 */
  animationConfig: any | null // 暂时使用any，后续需要定义具体的动画配置类型
  /** 时间轴项目的属性快照 */
  itemProperties: any // 暂时使用any，后续需要定义具体的属性类型
}

// ==================== 通用工具函数 ====================

/**
 * 通用的状态快照应用函数
 * 适配新架构的统一时间轴项目系统
 */
async function applyUnifiedKeyframeSnapshot(
  item: UnifiedTimelineItemData,
  snapshot: UnifiedKeyframeSnapshot,
  webavAnimationManager: { updateWebAVAnimation: (item: UnifiedTimelineItemData) => Promise<void> },
): Promise<void> {
  // 1. 恢复动画配置（关键帧数据）
  if (snapshot.animationConfig) {
    // 新架构中的动画配置恢复逻辑
    // 这里需要根据新架构的具体实现来调整
    console.log('🎬 [Unified Keyframe] 恢复动画配置:', snapshot.animationConfig)
  }

  // 2. 恢复属性值
  if (snapshot.itemProperties) {
    try {
      // 新架构中的属性恢复逻辑
      // 这里需要根据新架构的具体实现来调整
      console.log('🎬 [Unified Keyframe] 恢复项目属性:', snapshot.itemProperties)
      
      // 触发渲染更新
      // 在新架构中，这可能通过不同的方式实现
    } catch (error) {
      console.error('🎬 [Unified Keyframe] Failed to restore properties:', error)
      // 如果恢复失败，回退到直接更新
      Object.assign(item, snapshot.itemProperties)
    }
  }

  // 3. 更新WebAV动画配置
  await webavAnimationManager.updateWebAVAnimation(item)
}

// ==================== 创建关键帧命令 ====================

/**
 * 统一创建关键帧命令
 * 支持在指定帧位置创建包含所有属性的关键帧
 * 适配新架构的统一时间轴项目系统
 */
export class UnifiedCreateKeyframeCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string
  private beforeSnapshot: UnifiedKeyframeSnapshot
  private afterSnapshot: UnifiedKeyframeSnapshot | null = null

  constructor(
    private timelineItemId: string,
    private frame: number,
    private timelineModule: {
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    private webavAnimationManager: {
      updateWebAVAnimation: (item: UnifiedTimelineItemData) => Promise<void>
    },
    private playbackControls?: {
      seekTo: (frame: number) => void
    },
  ) {
    this.id = generateCommandId()
    this.description = `创建关键帧 (帧 ${frame})`

    // 保存执行前的状态快照
    const item = this.timelineModule.getTimelineItem(timelineItemId)
    if (!item) {
      throw new Error(`时间轴项目不存在: ${timelineItemId}`)
    }
    this.beforeSnapshot = this.createSnapshot(item)
  }

  /**
   * 创建状态快照
   */
  private createSnapshot(item: UnifiedTimelineItemData): UnifiedKeyframeSnapshot {
    return {
      animationConfig: item.config ? { ...item.config } : null, // 新架构中的动画配置
      itemProperties: { ...item.config }, // 使用完整的config作为快照
    }
  }

  /**
   * 应用状态快照
   */
  private async applySnapshot(item: UnifiedTimelineItemData, snapshot: UnifiedKeyframeSnapshot): Promise<void> {
    await applyUnifiedKeyframeSnapshot(item, snapshot, this.webavAnimationManager)
  }

  /**
   * 执行命令：创建关键帧
   */
  async execute(): Promise<void> {
    const item = this.timelineModule.getTimelineItem(this.timelineItemId)
    if (!item) {
      throw new Error(`时间轴项目不存在: ${this.timelineItemId}`)
    }

    // 检查播放头是否在clip时间范围内
    const isInRange = this.frame >= item.timeRange.timelineStartTime && 
                     this.frame <= item.timeRange.timelineEndTime
    
    if (!isInRange) {
      console.warn('🎬 [Unified Create Keyframe] 播放头不在当前clip时间范围内，无法创建关键帧:', {
        itemId: this.timelineItemId,
        frame: this.frame,
        clipTimeRange: {
          start: item.timeRange.timelineStartTime,
          end: item.timeRange.timelineEndTime,
        },
      })
      throw new Error('播放头不在当前clip时间范围内，无法创建关键帧')
    }

    try {
      // 动态导入统一关键帧工具函数
      const { createUnifiedKeyframe, enableUnifiedAnimation, initializeUnifiedAnimation } = await import(
        '../../utils/UnifiedKeyframeUtils'
      )

      // 1. 确保动画已启用
      const animation = (item.config as any).animation
      if (!animation) {
        initializeUnifiedAnimation(item)
      }
      enableUnifiedAnimation(item)

      // 2. 创建关键帧
      const keyframe = createUnifiedKeyframe(item, this.frame)
      const animationConfig = (item.config as any).animation
      animationConfig.keyframes.push(keyframe)

      // 3. 排序关键帧
      const { sortUnifiedKeyframes } = await import('../../utils/UnifiedKeyframeUtils')
      sortUnifiedKeyframes(item)

      // 4. 更新WebAV动画
      await this.webavAnimationManager.updateWebAVAnimation(item)

      // 5. 保存执行后的状态快照
      this.afterSnapshot = this.createSnapshot(item)

      // 6. 重做关键帧操作时，跳转到相关帧位置
      if (this.playbackControls) {
        this.playbackControls.seekTo(this.frame)
      }

      console.log('✅ 统一创建关键帧命令执行成功:', {
        itemId: this.timelineItemId,
        frame: this.frame,
      })
    } catch (error) {
      console.error('❌ 统一创建关键帧命令执行失败:', error)
      throw error
    }
  }

  /**
   * 撤销命令：恢复到创建前的状态
   */
  async undo(): Promise<void> {
    const item = this.timelineModule.getTimelineItem(this.timelineItemId)
    if (!item) {
      throw new Error(`时间轴项目不存在: ${this.timelineItemId}`)
    }

    try {
      await this.applySnapshot(item, this.beforeSnapshot)

      // 跳转到相关帧位置
      if (this.playbackControls) {
        this.playbackControls.seekTo(this.frame)
      }

      console.log('↩️ 统一创建关键帧命令撤销成功:', {
        itemId: this.timelineItemId,
        frame: this.frame,
      })
    } catch (error) {
      console.error('❌ 统一创建关键帧命令撤销失败:', error)
      throw error
    }
  }
}

// ==================== 删除关键帧命令 ====================

/**
 * 统一删除关键帧命令
 * 支持删除指定帧位置的关键帧
 * 适配新架构的统一时间轴项目系统
 */
export class UnifiedDeleteKeyframeCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string
  private beforeSnapshot: UnifiedKeyframeSnapshot
  private afterSnapshot: UnifiedKeyframeSnapshot | null = null

  constructor(
    private timelineItemId: string,
    private frame: number,
    private timelineModule: {
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    private webavAnimationManager: {
      updateWebAVAnimation: (item: UnifiedTimelineItemData) => Promise<void>
    },
    private playbackControls?: {
      seekTo: (frame: number) => void
    },
  ) {
    this.id = generateCommandId()
    this.description = `删除关键帧 (帧 ${frame})`

    // 保存执行前的状态快照
    const item = this.timelineModule.getTimelineItem(timelineItemId)
    if (!item) {
      throw new Error(`时间轴项目不存在: ${timelineItemId}`)
    }
    this.beforeSnapshot = this.createSnapshot(item)
  }

  /**
   * 创建状态快照
   */
  private createSnapshot(item: UnifiedTimelineItemData): UnifiedKeyframeSnapshot {
    return {
      animationConfig: item.config ? { ...item.config } : null,
      itemProperties: { ...item.config },
    }
  }

  /**
   * 应用状态快照
   */
  private async applySnapshot(item: UnifiedTimelineItemData, snapshot: UnifiedKeyframeSnapshot): Promise<void> {
    await applyUnifiedKeyframeSnapshot(item, snapshot, this.webavAnimationManager)
  }

  /**
   * 执行命令：删除关键帧
   */
  async execute(): Promise<void> {
    const item = this.timelineModule.getTimelineItem(this.timelineItemId)
    if (!item) {
      throw new Error(`时间轴项目不存在: ${this.timelineItemId}`)
    }

    // 检查播放头是否在clip时间范围内
    const isInRange = this.frame >= item.timeRange.timelineStartTime && 
                     this.frame <= item.timeRange.timelineEndTime
    
    if (!isInRange) {
      console.warn('🎬 [Unified Delete Keyframe] 播放头不在当前clip时间范围内，无法删除关键帧:', {
        itemId: this.timelineItemId,
        frame: this.frame,
        clipTimeRange: {
          start: item.timeRange.timelineStartTime,
          end: item.timeRange.timelineEndTime,
        },
      })
      throw new Error('播放头不在当前clip时间范围内，无法删除关键帧')
    }

    try {
      // 动态导入统一关键帧工具函数
      const { removeUnifiedKeyframeAtFrame, disableUnifiedAnimation } = await import(
        '../../utils/UnifiedKeyframeUtils'
      )

      // 删除指定帧的关键帧
      const removed = removeUnifiedKeyframeAtFrame(item, this.frame)

      if (!removed) {
        console.warn('🎬 [Unified Delete Keyframe] 指定帧没有关键帧可删除:', {
          itemId: this.timelineItemId,
          frame: this.frame,
        })
      }

      // 检查是否还有其他关键帧，如果没有则禁用动画
      const animation = (item.config as any).animation
      if (animation && animation.keyframes.length === 0) {
        disableUnifiedAnimation(item)
      }

      // 更新WebAV动画
      await this.webavAnimationManager.updateWebAVAnimation(item)

      // 保存执行后的状态快照
      this.afterSnapshot = this.createSnapshot(item)

      // 跳转到相关帧位置
      if (this.playbackControls) {
        this.playbackControls.seekTo(this.frame)
      }

      console.log('✅ 统一删除关键帧命令执行成功:', {
        itemId: this.timelineItemId,
        frame: this.frame,
        removed,
      })
    } catch (error) {
      console.error('❌ 统一删除关键帧命令执行失败:', error)
      throw error
    }
  }

  /**
   * 撤销命令：恢复到删除前的状态
   */
  async undo(): Promise<void> {
    const item = this.timelineModule.getTimelineItem(this.timelineItemId)
    if (!item) {
      throw new Error(`时间轴项目不存在: ${this.timelineItemId}`)
    }

    try {
      await this.applySnapshot(item, this.beforeSnapshot)

      // 跳转到相关帧位置
      if (this.playbackControls) {
        this.playbackControls.seekTo(this.frame)
      }

      console.log('↩️ 统一删除关键帧命令撤销成功:', {
        itemId: this.timelineItemId,
        frame: this.frame,
      })
    } catch (error) {
      console.error('❌ 统一删除关键帧命令撤销失败:', error)
      throw error
    }
  }
}

// ==================== 统一属性更新命令 ====================

/**
 * 统一属性更新命令
 * 根据当前动画状态智能处理属性修改：
 * - 无动画状态：直接更新属性
 * - 在关键帧上：更新现有关键帧
 * - 在关键帧之间：创建新关键帧
 * 适配新架构的统一时间轴项目系统
 */
export class UnifiedUpdatePropertyCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string
  private beforeSnapshot: UnifiedKeyframeSnapshot
  private afterSnapshot: UnifiedKeyframeSnapshot | null = null

  constructor(
    private timelineItemId: string,
    private frame: number,
    private property: string,
    private newValue: any,
    private timelineModule: {
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    private webavAnimationManager: {
      updateWebAVAnimation: (item: UnifiedTimelineItemData) => Promise<void>
    },
    private playbackControls?: {
      seekTo: (frame: number) => void
    },
  ) {
    this.id = generateCommandId()
    this.description = `修改属性: ${property} (帧 ${frame})`

    // 保存执行前的状态快照
    const item = this.timelineModule.getTimelineItem(timelineItemId)
    if (!item) {
      throw new Error(`时间轴项目不存在: ${timelineItemId}`)
    }
    this.beforeSnapshot = this.createSnapshot(item)
  }

  /**
   * 创建状态快照
   */
  private createSnapshot(item: UnifiedTimelineItemData): UnifiedKeyframeSnapshot {
    return {
      animationConfig: item.config ? { ...item.config } : null,
      itemProperties: { ...item.config },
    }
  }

  /**
   * 应用状态快照
   */
  private async applySnapshot(item: UnifiedTimelineItemData, snapshot: UnifiedKeyframeSnapshot): Promise<void> {
    await applyUnifiedKeyframeSnapshot(item, snapshot, this.webavAnimationManager)
  }

  /**
   * 执行命令：更新属性（智能处理关键帧）
   */
  async execute(): Promise<void> {
    const item = this.timelineModule.getTimelineItem(this.timelineItemId)
    if (!item) {
      throw new Error(`时间轴项目不存在: ${this.timelineItemId}`)
    }

    // 检查播放头是否在clip时间范围内
    const isInRange = this.frame >= item.timeRange.timelineStartTime &&
                     this.frame <= item.timeRange.timelineEndTime

    if (!isInRange) {
      console.warn('🎬 [Unified Update Property] 播放头不在当前clip时间范围内，无法更新属性:', {
        itemId: this.timelineItemId,
        frame: this.frame,
        property: this.property,
        value: this.newValue,
        clipTimeRange: {
          start: item.timeRange.timelineStartTime,
          end: item.timeRange.timelineEndTime,
        },
      })
      throw new Error('播放头不在当前clip时间范围内，无法更新属性')
    }

    try {
      // 在新架构中更新属性的逻辑
      console.log('🎬 [Unified Update Property] 更新属性:', {
        itemId: this.timelineItemId,
        frame: this.frame,
        property: this.property,
        value: this.newValue,
      })

      // 直接更新配置中的属性
      if (item.config && typeof item.config === 'object') {
        (item.config as any)[this.property] = this.newValue
      }

      // 更新WebAV动画
      await this.webavAnimationManager.updateWebAVAnimation(item)

      // 保存执行后的状态快照
      this.afterSnapshot = this.createSnapshot(item)

      // 跳转到相关帧位置
      if (this.playbackControls) {
        this.playbackControls.seekTo(this.frame)
      }

      console.log('✅ 统一属性更新命令执行成功:', {
        itemId: this.timelineItemId,
        frame: this.frame,
        property: this.property,
        value: this.newValue,
      })
    } catch (error) {
      console.error('❌ 统一属性更新命令执行失败:', error)
      throw error
    }
  }

  /**
   * 撤销命令：恢复到修改前的状态
   */
  async undo(): Promise<void> {
    const item = this.timelineModule.getTimelineItem(this.timelineItemId)
    if (!item) {
      throw new Error(`时间轴项目不存在: ${this.timelineItemId}`)
    }

    try {
      await this.applySnapshot(item, this.beforeSnapshot)

      // 跳转到相关帧位置
      if (this.playbackControls) {
        this.playbackControls.seekTo(this.frame)
      }

      console.log('↩️ 统一属性更新命令撤销成功:', {
        itemId: this.timelineItemId,
        frame: this.frame,
        property: this.property,
      })
    } catch (error) {
      console.error('❌ 统一属性更新命令撤销失败:', error)
      throw error
    }
  }
}

// ==================== 清除所有关键帧命令 ====================

/**
 * 统一清除所有关键帧命令
 * 支持清除时间轴项目的所有关键帧并禁用动画
 * 适配新架构的统一时间轴项目系统
 */
export class UnifiedClearAllKeyframesCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string
  private beforeSnapshot: UnifiedKeyframeSnapshot
  private afterSnapshot: UnifiedKeyframeSnapshot | null = null

  constructor(
    private timelineItemId: string,
    private timelineModule: {
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    private webavAnimationManager: {
      updateWebAVAnimation: (item: UnifiedTimelineItemData) => Promise<void>
    },
    private playbackControls?: {
      seekTo: (frame: number) => void
    },
  ) {
    this.id = generateCommandId()
    this.description = `清除所有关键帧`

    // 保存执行前的状态快照
    const item = this.timelineModule.getTimelineItem(timelineItemId)
    if (!item) {
      throw new Error(`时间轴项目不存在: ${timelineItemId}`)
    }
    this.beforeSnapshot = this.createSnapshot(item)
  }

  /**
   * 创建状态快照
   */
  private createSnapshot(item: UnifiedTimelineItemData): UnifiedKeyframeSnapshot {
    return {
      animationConfig: item.config ? { ...item.config } : null,
      itemProperties: { ...item.config },
    }
  }

  /**
   * 应用状态快照
   */
  private async applySnapshot(item: UnifiedTimelineItemData, snapshot: UnifiedKeyframeSnapshot): Promise<void> {
    await applyUnifiedKeyframeSnapshot(item, snapshot, this.webavAnimationManager)
  }

  /**
   * 执行命令：清除所有关键帧
   */
  async execute(): Promise<void> {
    const item = this.timelineModule.getTimelineItem(this.timelineItemId)
    if (!item) {
      throw new Error(`时间轴项目不存在: ${this.timelineItemId}`)
    }

    try {
      // 动态导入统一关键帧工具函数
      const { clearAllUnifiedKeyframes } = await import('../../utils/UnifiedKeyframeUtils')

      // 清除所有关键帧
      clearAllUnifiedKeyframes(item)

      // 更新WebAV动画
      await this.webavAnimationManager.updateWebAVAnimation(item)

      // 保存执行后的状态快照
      this.afterSnapshot = this.createSnapshot(item)

      // 跳转到时间轴项目的开始位置
      if (this.playbackControls && item.timeRange) {
        this.playbackControls.seekTo(item.timeRange.timelineStartTime)
      }

      console.log('✅ 统一清除所有关键帧命令执行成功:', {
        itemId: this.timelineItemId,
      })
    } catch (error) {
      console.error('❌ 统一清除所有关键帧命令执行失败:', error)
      throw error
    }
  }

  /**
   * 撤销命令：恢复到清除前的状态
   */
  async undo(): Promise<void> {
    const item = this.timelineModule.getTimelineItem(this.timelineItemId)
    if (!item) {
      throw new Error(`时间轴项目不存在: ${this.timelineItemId}`)
    }

    try {
      await this.applySnapshot(item, this.beforeSnapshot)

      // 撤销清除关键帧操作时，跳转到第一个关键帧位置
      if (this.playbackControls && this.beforeSnapshot.animationConfig) {
        // 这里需要根据新架构的具体实现来获取第一个关键帧位置
        this.playbackControls.seekTo(item.timeRange.timelineStartTime)
      }

      console.log('↩️ 统一清除所有关键帧命令撤销成功:', {
        itemId: this.timelineItemId,
      })
    } catch (error) {
      console.error('❌ 统一清除所有关键帧命令撤销失败:', error)
      throw error
    }
  }
}

// ==================== 切换关键帧命令 ====================

/**
 * 统一切换关键帧命令
 * 根据当前状态智能地创建或删除关键帧
 * 这是最常用的关键帧操作命令，对应关键帧按钮的点击行为
 * 适配新架构的统一时间轴项目系统
 */
export class UnifiedToggleKeyframeCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string
  private beforeSnapshot: UnifiedKeyframeSnapshot
  private afterSnapshot: UnifiedKeyframeSnapshot | null = null

  constructor(
    private timelineItemId: string,
    private frame: number,
    private timelineModule: {
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    private webavAnimationManager: {
      updateWebAVAnimation: (item: UnifiedTimelineItemData) => Promise<void>
    },
    private playbackControls?: {
      seekTo: (frame: number) => void
    },
  ) {
    this.id = generateCommandId()
    this.description = `切换关键帧 (帧 ${frame})`

    // 保存执行前的状态快照
    const item = this.timelineModule.getTimelineItem(timelineItemId)
    if (!item) {
      throw new Error(`时间轴项目不存在: ${timelineItemId}`)
    }
    this.beforeSnapshot = this.createSnapshot(item)
  }

  /**
   * 创建状态快照
   */
  private createSnapshot(item: UnifiedTimelineItemData): UnifiedKeyframeSnapshot {
    return {
      animationConfig: item.config ? { ...item.config } : null,
      itemProperties: { ...item.config },
    }
  }

  /**
   * 应用状态快照
   */
  private async applySnapshot(item: UnifiedTimelineItemData, snapshot: UnifiedKeyframeSnapshot): Promise<void> {
    await applyUnifiedKeyframeSnapshot(item, snapshot, this.webavAnimationManager)
  }

  /**
   * 执行命令：切换关键帧
   */
  async execute(): Promise<void> {
    const item = this.timelineModule.getTimelineItem(this.timelineItemId)
    if (!item) {
      throw new Error(`时间轴项目不存在: ${this.timelineItemId}`)
    }

    // 检查播放头是否在clip时间范围内
    const isInRange = this.frame >= item.timeRange.timelineStartTime &&
                     this.frame <= item.timeRange.timelineEndTime

    if (!isInRange) {
      console.warn('🎬 [Unified Toggle Keyframe] 播放头不在当前clip时间范围内，无法切换关键帧:', {
        itemId: this.timelineItemId,
        frame: this.frame,
        clipTimeRange: {
          start: item.timeRange.timelineStartTime,
          end: item.timeRange.timelineEndTime,
        },
      })
      throw new Error('播放头不在当前clip时间范围内，无法切换关键帧')
    }

    try {
      // 动态导入统一关键帧工具函数
      const { toggleUnifiedKeyframe } = await import('../../utils/UnifiedKeyframeUtils')

      // 使用统一的关键帧切换逻辑
      toggleUnifiedKeyframe(item, this.frame)

      // 更新WebAV动画
      await this.webavAnimationManager.updateWebAVAnimation(item)

      // 保存执行后的状态快照
      this.afterSnapshot = this.createSnapshot(item)

      // 跳转到相关帧位置
      if (this.playbackControls) {
        this.playbackControls.seekTo(this.frame)
      }

      console.log('✅ 统一切换关键帧命令执行成功:', {
        itemId: this.timelineItemId,
        frame: this.frame,
      })
    } catch (error) {
      console.error('❌ 统一切换关键帧命令执行失败:', error)
      throw error
    }
  }

  /**
   * 撤销命令：恢复到切换前的状态
   */
  async undo(): Promise<void> {
    const item = this.timelineModule.getTimelineItem(this.timelineItemId)
    if (!item) {
      throw new Error(`时间轴项目不存在: ${this.timelineItemId}`)
    }

    try {
      await this.applySnapshot(item, this.beforeSnapshot)

      // 跳转到相关帧位置
      if (this.playbackControls) {
        this.playbackControls.seekTo(this.frame)
      }

      console.log('↩️ 统一切换关键帧命令撤销成功:', {
        itemId: this.timelineItemId,
        frame: this.frame,
      })
    } catch (error) {
      console.error('❌ 统一切换关键帧命令撤销失败:', error)
      throw error
    }
  }
}
