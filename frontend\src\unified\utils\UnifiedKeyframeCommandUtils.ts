/**
 * 统一关键帧命令工具函数
 * 适配新架构的统一类型系统，提供通过命令系统执行关键帧操作的高级接口
 */

import type { UnifiedTimelineItemData } from '../timelineitem/TimelineItemData'
import type { SimpleCommand } from '../../types'
import {
  UnifiedCreateKeyframeCommand,
  UnifiedDeleteKeyframeCommand,
  UnifiedUpdatePropertyCommand,
  UnifiedClearAllKeyframesCommand,
  UnifiedToggleKeyframeCommand,
} from '../modules/commands/unifiedKeyframeCommands'

// ==================== 统一关键帧命令执行器接口 ====================

/**
 * 统一关键帧命令执行器接口
 * 适配新架构的统一类型系统
 */
export interface UnifiedKeyframeCommandExecutor {
  /** 时间轴模块 */
  timelineModule: {
    getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
  }
  /** WebAV动画管理器 */
  webavAnimationManager: {
    updateWebAVAnimation: (item: UnifiedTimelineItemData) => Promise<void>
  }
  /** 历史记录模块 */
  historyModule: {
    executeCommand: (command: SimpleCommand) => Promise<void>
  }
  /** 播放头控制器 */
  playbackControls?: {
    seekTo: (frame: number) => void
  }
}

/**
 * 统一批量关键帧操作
 * 适配新架构的统一类型系统
 */
export interface UnifiedBatchKeyframeOperation {
  type: 'create' | 'delete' | 'update' | 'clear' | 'toggle'
  timelineItemId: string
  frame?: number
  property?: string
  value?: any
}

// ==================== 关键帧命令执行函数 ====================

/**
 * 通过统一命令系统创建关键帧
 * @param timelineItemId 时间轴项目ID
 * @param frame 帧数
 * @param executor 命令执行器
 */
export async function createUnifiedKeyframeWithCommand(
  timelineItemId: string,
  frame: number,
  executor: UnifiedKeyframeCommandExecutor,
): Promise<void> {
  const command = new UnifiedCreateKeyframeCommand(
    timelineItemId,
    frame,
    executor.timelineModule,
    executor.webavAnimationManager,
    executor.playbackControls,
  )

  await executor.historyModule.executeCommand(command)
}

/**
 * 通过统一命令系统删除关键帧
 * @param timelineItemId 时间轴项目ID
 * @param frame 帧数
 * @param executor 命令执行器
 */
export async function deleteUnifiedKeyframeWithCommand(
  timelineItemId: string,
  frame: number,
  executor: UnifiedKeyframeCommandExecutor,
): Promise<void> {
  const command = new UnifiedDeleteKeyframeCommand(
    timelineItemId,
    frame,
    executor.timelineModule,
    executor.webavAnimationManager,
    executor.playbackControls,
  )

  await executor.historyModule.executeCommand(command)
}

/**
 * 通过统一命令系统更新属性（智能处理关键帧）
 * @param timelineItemId 时间轴项目ID
 * @param frame 帧数
 * @param property 属性名
 * @param value 新值
 * @param executor 命令执行器
 */
export async function updateUnifiedPropertyWithCommand(
  timelineItemId: string,
  frame: number,
  property: string,
  value: any,
  executor: UnifiedKeyframeCommandExecutor,
): Promise<void> {
  const command = new UnifiedUpdatePropertyCommand(
    timelineItemId,
    frame,
    property,
    value,
    executor.timelineModule,
    executor.webavAnimationManager,
    executor.playbackControls,
  )

  await executor.historyModule.executeCommand(command)
}

/**
 * 通过统一命令系统清除所有关键帧
 * @param timelineItemId 时间轴项目ID
 * @param executor 命令执行器
 */
export async function clearAllUnifiedKeyframesWithCommand(
  timelineItemId: string,
  executor: UnifiedKeyframeCommandExecutor,
): Promise<void> {
  const command = new UnifiedClearAllKeyframesCommand(
    timelineItemId,
    executor.timelineModule,
    executor.webavAnimationManager,
    executor.playbackControls,
  )

  await executor.historyModule.executeCommand(command)
}

/**
 * 通过统一命令系统切换关键帧
 * @param timelineItemId 时间轴项目ID
 * @param frame 帧数
 * @param executor 命令执行器
 */
export async function toggleUnifiedKeyframeWithCommand(
  timelineItemId: string,
  frame: number,
  executor: UnifiedKeyframeCommandExecutor,
): Promise<void> {
  const command = new UnifiedToggleKeyframeCommand(
    timelineItemId,
    frame,
    executor.timelineModule,
    executor.webavAnimationManager,
    executor.playbackControls,
  )

  await executor.historyModule.executeCommand(command)
}

// ==================== 命令执行器工厂函数 ====================

/**
 * 创建统一关键帧命令执行器
 * 从统一存储获取所需的模块依赖
 */
export async function createUnifiedKeyframeCommandExecutor(): Promise<UnifiedKeyframeCommandExecutor> {
  // 动态导入统一存储
  const { useUnifiedStore } = await import('../unifiedStore')
  const unifiedStore = useUnifiedStore()

  // 动态导入WebAV动画管理器（如果新架构中有对应的实现）
  // 这里需要根据新架构的具体实现来调整
  const webavAnimationManager = {
    updateWebAVAnimation: async (item: UnifiedTimelineItemData) => {
      // 新架构中的WebAV动画更新逻辑
      // 这里需要根据新架构的具体实现来调整
      console.log('🎬 [UnifiedKeyframeCommandUtils] 更新WebAV动画:', item.id)
    }
  }

  return {
    timelineModule: {
      getTimelineItem: (id: string) => unifiedStore.getTimelineItem(id),
    },
    webavAnimationManager,
    historyModule: {
      executeCommand: (command: SimpleCommand) => unifiedStore.executeCommand(command),
    },
    playbackControls: {
      seekTo: (frame: number) => {
        // 新架构中的播放头控制逻辑
        // 这里需要根据新架构的具体实现来调整
        console.log('🎯 [UnifiedKeyframeCommandUtils] 跳转到帧:', frame)
      },
    },
  }
}

// ==================== 便捷函数 ====================

/**
 * 便捷函数：创建统一关键帧
 * 自动创建执行器并执行命令
 */
export async function createUnifiedKeyframe(timelineItemId: string, frame: number): Promise<void> {
  const executor = await createUnifiedKeyframeCommandExecutor()
  await createUnifiedKeyframeWithCommand(timelineItemId, frame, executor)
}

/**
 * 便捷函数：删除统一关键帧
 * 自动创建执行器并执行命令
 */
export async function deleteUnifiedKeyframe(timelineItemId: string, frame: number): Promise<void> {
  const executor = await createUnifiedKeyframeCommandExecutor()
  await deleteUnifiedKeyframeWithCommand(timelineItemId, frame, executor)
}

/**
 * 便捷函数：更新统一属性（智能处理关键帧）
 * 自动创建执行器并执行命令
 */
export async function updateUnifiedProperty(
  timelineItemId: string,
  frame: number,
  property: string,
  value: any,
): Promise<void> {
  const executor = await createUnifiedKeyframeCommandExecutor()
  await updateUnifiedPropertyWithCommand(timelineItemId, frame, property, value, executor)
}

/**
 * 便捷函数：清除所有统一关键帧
 * 自动创建执行器并执行命令
 */
export async function clearAllUnifiedKeyframes(timelineItemId: string): Promise<void> {
  const executor = await createUnifiedKeyframeCommandExecutor()
  await clearAllUnifiedKeyframesWithCommand(timelineItemId, executor)
}

/**
 * 便捷函数：切换统一关键帧
 * 自动创建执行器并执行命令
 */
export async function toggleUnifiedKeyframe(timelineItemId: string, frame: number): Promise<void> {
  const executor = await createUnifiedKeyframeCommandExecutor()
  await toggleUnifiedKeyframeWithCommand(timelineItemId, frame, executor)
}

// ==================== 批量操作支持 ====================

/**
 * 统一智能批量关键帧命令
 * 适配新架构，在批量操作结束后智能选择播放头位置，避免频繁跳动
 */
class UnifiedSmartBatchKeyframeCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string
  private subCommands: SimpleCommand[] = []
  private playbackControls?: { seekTo: (frame: number) => void }

  constructor(
    description: string,
    commands: SimpleCommand[],
    playbackControls?: { seekTo: (frame: number) => void },
  ) {
    this.id = `unified_smart_batch_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
    this.description = description
    this.subCommands = [...commands]
    this.playbackControls = playbackControls
  }

  /**
   * 批量执行：依次执行所有子命令，但禁用子命令的播放头控制
   */
  async execute(): Promise<void> {
    // 临时禁用子命令的播放头控制
    const originalPlaybackControls = this.subCommands.map((cmd) => {
      const original = (cmd as any).playbackControls
      ;(cmd as any).playbackControls = undefined
      return original
    })

    try {
      // 执行所有子命令
      for (const command of this.subCommands) {
        await command.execute()
      }

      // 智能选择播放头位置
      this.smartSeekAfterBatch()
    } finally {
      // 恢复子命令的播放头控制
      this.subCommands.forEach((cmd, index) => {
        ;(cmd as any).playbackControls = originalPlaybackControls[index]
      })
    }
  }

  /**
   * 批量撤销：逆序撤销所有子命令，但禁用子命令的播放头控制
   */
  async undo(): Promise<void> {
    // 临时禁用子命令的播放头控制
    const originalPlaybackControls = this.subCommands.map((cmd) => {
      const original = (cmd as any).playbackControls
      ;(cmd as any).playbackControls = undefined
      return original
    })

    try {
      // 逆序撤销所有子命令
      for (let i = this.subCommands.length - 1; i >= 0; i--) {
        await this.subCommands[i].undo()
      }

      // 智能选择播放头位置
      this.smartSeekAfterBatch()
    } finally {
      // 恢复子命令的播放头控制
      this.subCommands.forEach((cmd, index) => {
        ;(cmd as any).playbackControls = originalPlaybackControls[index]
      })
    }
  }

  /**
   * 智能选择播放头位置
   * 根据批量操作的类型和涉及的帧位置选择最合适的播放头位置
   */
  private smartSeekAfterBatch(): void {
    if (!this.playbackControls) return

    // 收集所有涉及的帧位置
    const frames: number[] = []
    for (const cmd of this.subCommands) {
      const frame = (cmd as any).frame
      if (frame !== undefined) {
        frames.push(frame)
      }
    }

    if (frames.length === 0) return

    // 选择策略：跳转到最后一个操作的帧位置
    // 这样用户可以看到最后一个操作的效果
    const targetFrame = frames[frames.length - 1]
    this.playbackControls.seekTo(targetFrame)

    console.log('🎯 统一智能批量操作播放头控制:', {
      totalOperations: this.subCommands.length,
      involvedFrames: frames,
      targetFrame,
    })
  }
}

/**
 * 执行统一批量关键帧操作
 * @param operations 操作列表
 */
export async function executeUnifiedBatchKeyframeOperations(
  operations: UnifiedBatchKeyframeOperation[],
): Promise<void> {
  const executor = await createUnifiedKeyframeCommandExecutor()

  // 创建子命令，但不传递播放头控制器（由批量命令统一管理）
  const commands: SimpleCommand[] = []

  for (const op of operations) {
    switch (op.type) {
      case 'create':
        if (op.frame !== undefined) {
          commands.push(
            new UnifiedCreateKeyframeCommand(
              op.timelineItemId,
              op.frame,
              executor.timelineModule,
              executor.webavAnimationManager,
              executor.playbackControls, // 这里传递，但会在批量执行时临时禁用
            ),
          )
        }
        break
      case 'delete':
        if (op.frame !== undefined) {
          commands.push(
            new UnifiedDeleteKeyframeCommand(
              op.timelineItemId,
              op.frame,
              executor.timelineModule,
              executor.webavAnimationManager,
              executor.playbackControls,
            ),
          )
        }
        break
      case 'update':
        if (op.frame !== undefined && op.property && op.value !== undefined) {
          commands.push(
            new UnifiedUpdatePropertyCommand(
              op.timelineItemId,
              op.frame,
              op.property,
              op.value,
              executor.timelineModule,
              executor.webavAnimationManager,
              executor.playbackControls,
            ),
          )
        }
        break
      case 'clear':
        commands.push(
          new UnifiedClearAllKeyframesCommand(
            op.timelineItemId,
            executor.timelineModule,
            executor.webavAnimationManager,
            executor.playbackControls,
          ),
        )
        break
      case 'toggle':
        if (op.frame !== undefined) {
          commands.push(
            new UnifiedToggleKeyframeCommand(
              op.timelineItemId,
              op.frame,
              executor.timelineModule,
              executor.webavAnimationManager,
              executor.playbackControls,
            ),
          )
        }
        break
    }
  }

  if (commands.length > 0) {
    const batchCommand = new UnifiedSmartBatchKeyframeCommand(
      '统一批量关键帧操作',
      commands,
      executor.playbackControls,
    )
    await executor.historyModule.executeCommand(batchCommand)
  }
}
