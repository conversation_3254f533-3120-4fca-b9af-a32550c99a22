import { generateCommandId } from '../../../utils/idGenerator'
import type { SimpleCommand } from '../../../types'
import type { UnifiedTimelineItemData, TransformData } from '../../timelineitem/TimelineItemData'
import type { UnifiedMediaItemData } from '../../mediaitem/types'
import type { VideoTimeRange, ImageTimeRange } from '../../../types'



// ==================== 添加时间轴项目命令 ====================

/**
 * 统一添加时间轴项目命令
 * 支持添加统一时间轴项目的撤销/重做操作
 * 适配新架构的统一类型系统
 */
export class UnifiedAddTimelineItemCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string
  private originalTimelineItemData: UnifiedTimelineItemData | null = null

  constructor(
    timelineItem: UnifiedTimelineItemData,
    private timelineModule: {
      addTimelineItem: (item: UnifiedTimelineItemData) => void
      removeTimelineItem: (id: string) => void
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    private mediaModule: {
      getMediaItem: (id: string) => UnifiedMediaItemData | undefined
    },
    private webavModule?: {
      addSprite: (sprite: any) => Promise<boolean>
      removeSprite: (sprite: any) => boolean
    },
  ) {
    this.id = generateCommandId()

    const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
    this.description = `添加时间轴项目: ${mediaItem?.name || '未知素材'}`

    // 保存原始数据用于重建
    this.originalTimelineItemData = { ...timelineItem }
  }

  /**
   * 执行命令：添加时间轴项目
   */
  async execute(): Promise<void> {
    try {
      if (!this.originalTimelineItemData) {
        throw new Error('时间轴项目数据不存在')
      }

      console.log(`🔄 执行添加操作：添加统一时间轴项目...`)

      // 1. 添加到时间轴
      this.timelineModule.addTimelineItem(this.originalTimelineItemData)

      // 2. 如果有webav模块，添加sprite到WebAV画布
      if (this.webavModule && this.originalTimelineItemData.sprite) {
        await this.webavModule.addSprite(this.originalTimelineItemData.sprite)
      }

      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItemData.mediaItemId)
      console.log(`✅ 已添加统一时间轴项目: ${mediaItem?.name || '未知素材'}`)
    } catch (error) {
      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItemData?.mediaItemId || '')
      console.error(`❌ 添加时间轴项目失败: ${mediaItem?.name || '未知项目'}`, error)
      throw error
    }
  }

  /**
   * 撤销命令：移除时间轴项目
   */
  async undo(): Promise<void> {
    try {
      if (!this.originalTimelineItemData) {
        console.warn('⚠️ 时间轴项目数据不存在，无法撤销')
        return
      }

      const existingItem = this.timelineModule.getTimelineItem(this.originalTimelineItemData.id)
      if (!existingItem) {
        console.warn(`⚠️ 时间轴项目不存在，无法撤销: ${this.originalTimelineItemData.id}`)
        return
      }

      // 移除时间轴项目
      this.timelineModule.removeTimelineItem(this.originalTimelineItemData.id)

      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItemData.mediaItemId)
      console.log(`↩️ 已撤销添加时间轴项目: ${mediaItem?.name || '未知素材'}`)
    } catch (error) {
      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItemData?.mediaItemId || '')
      console.error(`❌ 撤销添加时间轴项目失败: ${mediaItem?.name || '未知项目'}`, error)
      throw error
    }
  }
}

// ==================== 移除时间轴项目命令 ====================

/**
 * 统一移除时间轴项目命令
 * 支持移除统一时间轴项目的撤销/重做操作
 * 适配新架构的统一类型系统
 */
export class UnifiedRemoveTimelineItemCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string
  private originalTimelineItemData: UnifiedTimelineItemData | null = null

  constructor(
    private timelineItemId: string,
    timelineItem: UnifiedTimelineItemData,
    private timelineModule: {
      addTimelineItem: (item: UnifiedTimelineItemData) => void
      removeTimelineItem: (id: string) => void
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    private mediaModule: {
      getMediaItem: (id: string) => UnifiedMediaItemData | undefined
    },
    private webavModule?: {
      addSprite: (sprite: any) => Promise<boolean>
      removeSprite: (sprite: any) => boolean
    },
  ) {
    this.id = generateCommandId()

    const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
    this.description = `移除时间轴项目: ${mediaItem?.name || '未知素材'}`

    // 保存完整的项目数据用于撤销
    this.originalTimelineItemData = { ...timelineItem }

    console.log('💾 保存删除项目的数据:', {
      id: this.originalTimelineItemData.id,
      mediaItemId: this.originalTimelineItemData.mediaItemId,
      mediaType: this.originalTimelineItemData.mediaType,
      timeRange: this.originalTimelineItemData.timeRange,
    })
  }

  /**
   * 执行命令：删除时间轴项目
   */
  async execute(): Promise<void> {
    try {
      // 检查项目是否存在
      const existingItem = this.timelineModule.getTimelineItem(this.timelineItemId)
      if (!existingItem) {
        console.warn(`⚠️ 时间轴项目不存在，无法删除: ${this.timelineItemId}`)
        return
      }

      // 删除时间轴项目
      this.timelineModule.removeTimelineItem(this.timelineItemId)

      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItemData?.mediaItemId || '')
      console.log(`🗑️ 已删除统一时间轴项目: ${mediaItem?.name || '未知素材'}`)
    } catch (error) {
      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItemData?.mediaItemId || '')
      console.error(`❌ 删除时间轴项目失败: ${mediaItem?.name || '未知项目'}`, error)
      throw error
    }
  }

  /**
   * 撤销命令：重新创建时间轴项目
   */
  async undo(): Promise<void> {
    try {
      if (!this.originalTimelineItemData) {
        throw new Error('没有有效的时间轴项目数据')
      }

      console.log(`🔄 撤销删除操作：重建统一时间轴项目...`)

      // 1. 添加到时间轴
      this.timelineModule.addTimelineItem(this.originalTimelineItemData)

      // 2. 如果有webav模块，添加sprite到WebAV画布
      if (this.webavModule && this.originalTimelineItemData.sprite) {
        await this.webavModule.addSprite(this.originalTimelineItemData.sprite)
      }

      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItemData.mediaItemId)
      console.log(`↩️ 已撤销删除统一时间轴项目: ${mediaItem?.name || '未知素材'}`)
    } catch (error) {
      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItemData?.mediaItemId || '')
      console.error(`❌ 撤销删除时间轴项目失败: ${mediaItem?.name || '未知项目'}`, error)
      throw error
    }
  }
}

// ==================== 移动时间轴项目命令 ====================

/**
 * 统一移动时间轴项目命令
 * 支持统一时间轴项目位置移动的撤销/重做操作
 * 包括时间位置移动和轨道间移动
 */
export class UnifiedMoveTimelineItemCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string

  constructor(
    private timelineItemId: string,
    private oldPositionFrames: number, // 旧的时间位置（帧数）
    private newPositionFrames: number, // 新的时间位置（帧数）
    private oldTrackId: string, // 旧的轨道ID
    private newTrackId: string, // 新的轨道ID
    private timelineModule: {
      updateTimelineItemPosition: (id: string, positionFrames: number, trackId?: string) => void
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    private mediaModule: {
      getMediaItem: (id: string) => UnifiedMediaItemData | undefined
    },
  ) {
    this.id = generateCommandId()

    const timelineItem = this.timelineModule.getTimelineItem(timelineItemId)
    let itemName = '未知素材'

    // 根据项目类型获取名称
    if (timelineItem) {
      const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
      itemName = mediaItem?.name || '未知素材'
    }

    // 生成描述信息
    const positionChanged = this.oldPositionFrames !== this.newPositionFrames
    const trackChanged = oldTrackId !== newTrackId

    if (positionChanged && trackChanged) {
      this.description = `移动时间轴项目: ${itemName} (位置: ${this.oldPositionFrames}帧→${this.newPositionFrames}帧, 轨道: ${oldTrackId}→${newTrackId})`
    } else if (positionChanged) {
      this.description = `移动时间轴项目: ${itemName} (位置: ${this.oldPositionFrames}帧→${this.newPositionFrames}帧)`
    } else if (trackChanged) {
      this.description = `移动时间轴项目: ${itemName} (轨道: ${oldTrackId}→${newTrackId})`
    } else {
      this.description = `移动时间轴项目: ${itemName} (无变化)`
    }

    console.log('💾 保存移动操作数据:', {
      timelineItemId,
      oldPositionFrames: this.oldPositionFrames,
      newPositionFrames: this.newPositionFrames,
      oldTrackId,
      newTrackId,
      positionChanged,
      trackChanged,
    })
  }

  /**
   * 执行命令：移动时间轴项目到新位置
   */
  async execute(): Promise<void> {
    try {
      // 检查项目是否存在
      const timelineItem = this.timelineModule.getTimelineItem(this.timelineItemId)
      if (!timelineItem) {
        console.warn(`⚠️ 时间轴项目不存在，无法移动: ${this.timelineItemId}`)
        return
      }

      // 移动到新位置
      const trackIdToSet = this.oldTrackId !== this.newTrackId ? this.newTrackId : undefined
      this.timelineModule.updateTimelineItemPosition(
        this.timelineItemId,
        this.newPositionFrames,
        trackIdToSet,
      )

      // 获取项目名称
      const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
      const itemName = mediaItem?.name || '未知素材'

      console.log(
        `🔄 已移动时间轴项目: ${itemName} 到位置 ${this.newPositionFrames}帧, 轨道 ${this.newTrackId}`,
      )
    } catch (error) {
      const timelineItem = this.timelineModule.getTimelineItem(this.timelineItemId)
      let itemName = '未知素材'
      if (timelineItem) {
        const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
        itemName = mediaItem?.name || '未知素材'
      }
      console.error(`❌ 移动时间轴项目失败: ${itemName}`, error)
      throw error
    }
  }

  /**
   * 撤销命令：移动时间轴项目回到原位置
   */
  async undo(): Promise<void> {
    try {
      // 检查项目是否存在
      const timelineItem = this.timelineModule.getTimelineItem(this.timelineItemId)
      if (!timelineItem) {
        console.warn(`⚠️ 时间轴项目不存在，无法撤销移动: ${this.timelineItemId}`)
        return
      }

      // 移动回原位置
      const trackIdToSet = this.oldTrackId !== this.newTrackId ? this.oldTrackId : undefined
      this.timelineModule.updateTimelineItemPosition(
        this.timelineItemId,
        this.oldPositionFrames,
        trackIdToSet,
      )

      // 获取项目名称
      const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
      const itemName = mediaItem?.name || '未知素材'

      console.log(
        `↩️ 已撤销移动时间轴项目: ${itemName} 回到位置 ${this.oldPositionFrames}帧, 轨道 ${this.oldTrackId}`,
      )
    } catch (error) {
      const timelineItem = this.timelineModule.getTimelineItem(this.timelineItemId)
      let itemName = '未知素材'
      if (timelineItem) {
        const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
        itemName = mediaItem?.name || '未知素材'
      }
      console.error(`❌ 撤销移动时间轴项目失败: ${itemName}`, error)
      throw error
    }
  }
}

// ==================== 复制时间轴项目命令 ====================

/**
 * 统一复制时间轴项目命令
 * 支持复制统一时间轴项目的撤销/重做操作
 * 适配新架构的统一类型系统
 */
export class UnifiedDuplicateTimelineItemCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string
  private originalTimelineItemData: UnifiedTimelineItemData | null = null
  public readonly newTimelineItemId: string // 新创建的项目ID

  constructor(
    originalTimelineItemId: string,
    originalTimelineItem: UnifiedTimelineItemData,
    private newPositionFrames: number, // 新项目的时间位置（帧数）
    private timelineModule: {
      addTimelineItem: (item: UnifiedTimelineItemData) => void
      removeTimelineItem: (id: string) => void
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    private mediaModule: {
      getMediaItem: (id: string) => UnifiedMediaItemData | undefined
    },
    private webavModule?: {
      addSprite: (sprite: any) => Promise<boolean>
      removeSprite: (sprite: any) => boolean
    },
  ) {
    this.id = generateCommandId()

    const mediaItem = this.mediaModule.getMediaItem(originalTimelineItem.mediaItemId)
    this.description = `复制时间轴项目: ${mediaItem?.name || '未知素材'}`

    // 保存原始项目的完整数据
    this.originalTimelineItemData = { ...originalTimelineItem }

    // 生成新项目的ID
    this.newTimelineItemId = `timeline_item_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 重建复制的时间轴项目
   */
  private rebuildDuplicatedItem(): UnifiedTimelineItemData {
    if (!this.originalTimelineItemData) {
      throw new Error('原始时间轴项目数据不存在')
    }

    console.log('🔄 [UnifiedDuplicateTimelineItemCommand] 重建复制的时间轴项目...')

    // 计算新的时间范围
    const originalTimeRange = this.originalTimelineItemData.timeRange
    const originalDurationFrames = originalTimeRange.timelineEndTime - originalTimeRange.timelineStartTime
    const newTimelineStartTimeFrames = this.newPositionFrames
    const newTimelineEndTimeFrames = newTimelineStartTimeFrames + originalDurationFrames

    // 创建新的时间轴项目
    const newTimelineItem: UnifiedTimelineItemData = {
      ...this.originalTimelineItemData,
      id: this.newTimelineItemId,
      timeRange: {
        timelineStartTime: newTimelineStartTimeFrames,
        timelineEndTime: newTimelineEndTimeFrames,
      },
      // 复制配置但不共享引用
      config: { ...this.originalTimelineItemData.config },
      // sprite需要重新创建，这里暂时设为undefined
      sprite: undefined,
    }

    console.log('✅ [UnifiedDuplicateTimelineItemCommand] 复制的时间轴项目重建完成')
    return newTimelineItem
  }

  /**
   * 执行命令：创建复制的时间轴项目
   */
  async execute(): Promise<void> {
    try {
      console.log(`🔄 执行复制操作：从源头重建时间轴项目...`)

      // 重建复制的TimelineItem
      const newTimelineItem = this.rebuildDuplicatedItem()

      // 1. 添加到时间轴
      this.timelineModule.addTimelineItem(newTimelineItem)

      // 2. 如果有webav模块，添加sprite到WebAV画布
      if (this.webavModule && newTimelineItem.sprite) {
        await this.webavModule.addSprite(newTimelineItem.sprite)
      }

      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItemData?.mediaItemId || '')
      console.log(`✅ 已复制统一时间轴项目: ${mediaItem?.name || '未知素材'}`)
    } catch (error) {
      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItemData?.mediaItemId || '')
      console.error(`❌ 复制时间轴项目失败: ${mediaItem?.name || '未知项目'}`, error)
      throw error
    }
  }

  /**
   * 撤销命令：删除复制的时间轴项目
   */
  async undo(): Promise<void> {
    try {
      console.log(`🔄 撤销复制操作：删除复制的时间轴项目...`)

      // 删除复制的时间轴项目
      this.timelineModule.removeTimelineItem(this.newTimelineItemId)

      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItemData?.mediaItemId || '')
      console.log(`↩️ 已撤销复制统一时间轴项目: ${mediaItem?.name || '未知素材'}`)
    } catch (error) {
      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItemData?.mediaItemId || '')
      console.error(`❌ 撤销复制时间轴项目失败: ${mediaItem?.name || '未知项目'}`, error)
      throw error
    }
  }
}

// ==================== 更新变换属性命令 ====================

/**
 * 统一更新变换属性命令
 * 支持变换属性修改的撤销/重做操作
 * 适配新架构的统一类型系统
 */
export class UnifiedUpdateTransformCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string

  constructor(
    private timelineItemId: string,
    propertyType: 'position' | 'size' | 'rotation' | 'opacity' | 'zIndex' | 'multiple',
    private oldValues: TransformData,
    private newValues: TransformData,
    private timelineModule: {
      updateTimelineItemTransform: (id: string, transform: TransformData) => void
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    private mediaModule: {
      getMediaItem: (id: string) => UnifiedMediaItemData | undefined
    },
  ) {
    this.id = generateCommandId()

    const timelineItem = this.timelineModule.getTimelineItem(timelineItemId)
    const mediaItem = timelineItem
      ? this.mediaModule.getMediaItem(timelineItem.mediaItemId)
      : null

    // 生成描述信息
    this.description = this.generateDescription(mediaItem?.name || '未知素材')

    console.log('💾 保存变换属性操作数据:', {
      timelineItemId,
      propertyType,
      oldValues,
      newValues,
    })
  }

  /**
   * 生成命令描述
   */
  private generateDescription(mediaName: string): string {
    const changes: string[] = []

    // 检查位置变化
    if (
      (this.newValues.x !== undefined && this.oldValues.x !== undefined) ||
      (this.newValues.y !== undefined && this.oldValues.y !== undefined)
    ) {
      const oldX = this.oldValues.x ?? 0
      const oldY = this.oldValues.y ?? 0
      const newX = this.newValues.x ?? oldX
      const newY = this.newValues.y ?? oldY
      changes.push(
        `位置: (${oldX.toFixed(0)}, ${oldY.toFixed(0)}) → (${newX.toFixed(0)}, ${newY.toFixed(0)})`,
      )
    }

    // 检查大小变化
    if (
      (this.newValues.width !== undefined && this.oldValues.width !== undefined) ||
      (this.newValues.height !== undefined && this.oldValues.height !== undefined)
    ) {
      const oldWidth = this.oldValues.width ?? 0
      const oldHeight = this.oldValues.height ?? 0
      const newWidth = this.newValues.width ?? oldWidth
      const newHeight = this.newValues.height ?? oldHeight
      changes.push(
        `大小: ${oldWidth.toFixed(0)}×${oldHeight.toFixed(0)} → ${newWidth.toFixed(0)}×${newHeight.toFixed(0)}`,
      )
    }

    if (this.newValues.rotation !== undefined && this.oldValues.rotation !== undefined) {
      // 将弧度转换为角度显示
      const oldDegrees = ((this.oldValues.rotation * 180) / Math.PI).toFixed(1)
      const newDegrees = ((this.newValues.rotation * 180) / Math.PI).toFixed(1)
      changes.push(`旋转: ${oldDegrees}° → ${newDegrees}°`)
    }

    if (this.newValues.opacity !== undefined && this.oldValues.opacity !== undefined) {
      const oldOpacity = (this.oldValues.opacity * 100).toFixed(0)
      const newOpacity = (this.newValues.opacity * 100).toFixed(0)
      changes.push(`透明度: ${oldOpacity}% → ${newOpacity}%`)
    }

    if (this.newValues.zIndex !== undefined && this.oldValues.zIndex !== undefined) {
      changes.push(`层级: ${this.oldValues.zIndex} → ${this.newValues.zIndex}`)
    }

    const changeText = changes.length > 0 ? ` (${changes.join(', ')})` : ''
    return `更新变换属性: ${mediaName}${changeText}`
  }

  /**
   * 执行命令：应用新的变换属性
   */
  async execute(): Promise<void> {
    try {
      // 检查项目是否存在
      const timelineItem = this.timelineModule.getTimelineItem(this.timelineItemId)
      if (!timelineItem) {
        console.warn(`⚠️ 时间轴项目不存在，无法更新变换属性: ${this.timelineItemId}`)
        return
      }

      // 应用新的变换属性
      this.timelineModule.updateTimelineItemTransform(this.timelineItemId, this.newValues)

      const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
      console.log(`🔄 已更新变换属性: ${mediaItem?.name || '未知素材'}`)
    } catch (error) {
      const timelineItem = this.timelineModule.getTimelineItem(this.timelineItemId)
      let itemName = '未知素材'
      if (timelineItem) {
        const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
        itemName = mediaItem?.name || '未知素材'
      }
      console.error(`❌ 更新变换属性失败: ${itemName}`, error)
      throw error
    }
  }

  /**
   * 撤销命令：恢复到修改前的变换属性
   */
  async undo(): Promise<void> {
    try {
      // 检查项目是否存在
      const timelineItem = this.timelineModule.getTimelineItem(this.timelineItemId)
      if (!timelineItem) {
        console.warn(`⚠️ 时间轴项目不存在，无法撤销变换属性更新: ${this.timelineItemId}`)
        return
      }

      // 恢复旧的变换属性
      this.timelineModule.updateTimelineItemTransform(this.timelineItemId, this.oldValues)

      const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
      console.log(`↩️ 已撤销变换属性更新: ${mediaItem?.name || '未知素材'}`)
    } catch (error) {
      const timelineItem = this.timelineModule.getTimelineItem(this.timelineItemId)
      let itemName = '未知素材'
      if (timelineItem) {
        const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
        itemName = mediaItem?.name || '未知素材'
      }
      console.error(`❌ 撤销变换属性更新失败: ${itemName}`, error)
      throw error
    }
  }
}

// ==================== 调整时间轴项目大小命令 ====================

/**
 * 统一调整时间轴项目大小命令
 * 支持调整时间范围的撤销/重做操作
 * 适配新架构的统一类型系统
 */
export class UnifiedResizeTimelineItemCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string
  private oldTimeRange: VideoTimeRange | ImageTimeRange
  private newTimeRange: VideoTimeRange | ImageTimeRange

  constructor(
    private timelineItemId: string,
    newTimeRange: VideoTimeRange | ImageTimeRange,
    private timelineModule: {
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
      updateTimelineItemPosition: (id: string, position: number, trackId?: string) => void
    },
    private mediaModule: {
      getMediaItem: (id: string) => UnifiedMediaItemData | undefined
    },
  ) {
    this.id = generateCommandId()

    // 获取当前时间范围
    const timelineItem = this.timelineModule.getTimelineItem(timelineItemId)
    if (!timelineItem) {
      throw new Error(`时间轴项目不存在: ${timelineItemId}`)
    }

    this.oldTimeRange = timelineItem.timeRange as VideoTimeRange | ImageTimeRange
    this.newTimeRange = newTimeRange

    const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
    this.description = `调整时间范围: ${mediaItem?.name || '未知素材'}`
  }

  async execute(): Promise<void> {
    try {
      console.log(`🔄 [UnifiedResizeTimelineItemCommand] 执行调整时间范围操作...`)

      const timelineItem = this.timelineModule.getTimelineItem(this.timelineItemId)
      if (!timelineItem) {
        throw new Error(`时间轴项目不存在: ${this.timelineItemId}`)
      }

      // 更新时间范围
      timelineItem.timeRange = this.newTimeRange

      // 如果位置发生变化，更新位置
      if (this.oldTimeRange.timelineStartTime !== this.newTimeRange.timelineStartTime) {
        this.timelineModule.updateTimelineItemPosition(
          this.timelineItemId,
          this.newTimeRange.timelineStartTime,
          timelineItem.trackId
        )
      }

      const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
      console.log(`✅ [UnifiedResizeTimelineItemCommand] 时间范围调整成功: ${mediaItem?.name || '未知素材'}`)
    } catch (error) {
      console.error(`❌ [UnifiedResizeTimelineItemCommand] 调整时间范围失败:`, error)
      throw error
    }
  }

  async undo(): Promise<void> {
    try {
      console.log(`🔄 [UnifiedResizeTimelineItemCommand] 撤销调整时间范围操作...`)

      const timelineItem = this.timelineModule.getTimelineItem(this.timelineItemId)
      if (!timelineItem) {
        throw new Error(`时间轴项目不存在: ${this.timelineItemId}`)
      }

      // 恢复原始时间范围
      timelineItem.timeRange = this.oldTimeRange

      // 如果位置发生变化，恢复位置
      if (this.oldTimeRange.timelineStartTime !== this.newTimeRange.timelineStartTime) {
        this.timelineModule.updateTimelineItemPosition(
          this.timelineItemId,
          this.oldTimeRange.timelineStartTime,
          timelineItem.trackId
        )
      }

      const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
      console.log(`✅ [UnifiedResizeTimelineItemCommand] 时间范围恢复成功: ${mediaItem?.name || '未知素材'}`)
    } catch (error) {
      console.error(`❌ [UnifiedResizeTimelineItemCommand] 撤销调整时间范围失败:`, error)
      throw error
    }
  }
}

// ==================== 分割时间轴项目命令 ====================

/**
 * 统一分割时间轴项目命令
 * 支持分割时间轴项目的撤销/重做操作
 * 适配新架构的统一类型系统
 */
export class UnifiedSplitTimelineItemCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string
  private originalTimelineItem: UnifiedTimelineItemData | null = null
  private firstPartItem: UnifiedTimelineItemData | null = null
  private secondPartItem: UnifiedTimelineItemData | null = null
  public readonly firstPartId: string
  public readonly secondPartId: string

  constructor(
    private timelineItemId: string,
    private splitTimeFrames: number,
    private timelineModule: {
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
      addTimelineItem: (item: UnifiedTimelineItemData) => void
      removeTimelineItem: (id: string) => void
    },
    private mediaModule: {
      getMediaItem: (id: string) => UnifiedMediaItemData | undefined
    },
  ) {
    this.id = generateCommandId()
    this.firstPartId = `${timelineItemId}_part1_${Date.now()}`
    this.secondPartId = `${timelineItemId}_part2_${Date.now()}`

    const timelineItem = this.timelineModule.getTimelineItem(timelineItemId)
    const mediaItem = timelineItem ? this.mediaModule.getMediaItem(timelineItem.mediaItemId) : null
    this.description = `分割时间轴项目: ${mediaItem?.name || '未知素材'}`
  }

  async execute(): Promise<void> {
    try {
      console.log(`🔄 [UnifiedSplitTimelineItemCommand] 执行分割时间轴项目操作...`)

      // 获取原始项目
      const originalItem = this.timelineModule.getTimelineItem(this.timelineItemId)
      if (!originalItem) {
        throw new Error(`时间轴项目不存在: ${this.timelineItemId}`)
      }
      this.originalTimelineItem = originalItem

      const originalTimeRange = this.originalTimelineItem.timeRange

      // 创建第一部分（开始到分割点）
      this.firstPartItem = {
        ...this.originalTimelineItem,
        id: this.firstPartId,
        timeRange: {
          ...originalTimeRange,
          timelineEndTime: this.splitTimeFrames,
        },
        sprite: undefined, // 需要重新创建
      }

      // 创建第二部分（分割点到结束）
      this.secondPartItem = {
        ...this.originalTimelineItem,
        id: this.secondPartId,
        timeRange: {
          ...originalTimeRange,
          timelineStartTime: this.splitTimeFrames,
        },
        sprite: undefined, // 需要重新创建
      }

      // 删除原始项目
      this.timelineModule.removeTimelineItem(this.timelineItemId)

      // 添加分割后的两个项目
      this.timelineModule.addTimelineItem(this.firstPartItem)
      this.timelineModule.addTimelineItem(this.secondPartItem)

      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItem.mediaItemId)
      console.log(`✅ [UnifiedSplitTimelineItemCommand] 时间轴项目分割成功: ${mediaItem?.name || '未知素材'}`)
    } catch (error) {
      console.error(`❌ [UnifiedSplitTimelineItemCommand] 分割时间轴项目失败:`, error)
      throw error
    }
  }

  async undo(): Promise<void> {
    try {
      console.log(`🔄 [UnifiedSplitTimelineItemCommand] 撤销分割时间轴项目操作...`)

      if (!this.originalTimelineItem) {
        throw new Error('原始时间轴项目数据不存在')
      }

      // 删除分割后的两个项目
      this.timelineModule.removeTimelineItem(this.firstPartId)
      this.timelineModule.removeTimelineItem(this.secondPartId)

      // 恢复原始项目
      this.timelineModule.addTimelineItem(this.originalTimelineItem)

      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItem.mediaItemId)
      console.log(`✅ [UnifiedSplitTimelineItemCommand] 时间轴项目分割撤销成功: ${mediaItem?.name || '未知素材'}`)
    } catch (error) {
      console.error(`❌ [UnifiedSplitTimelineItemCommand] 撤销分割时间轴项目失败:`, error)
      throw error
    }
  }
}
