import { markRaw, reactive } from 'vue'
import { TextVisibleSprite } from '../../utils/TextVisibleSprite'
import { webavToProjectCoords } from '../../utils/coordinateTransform'
import { generateUUID4 } from '../../utils/idGenerator'
import type { UnifiedTimelineItemData, BasicTimelineConfig, TransformData } from '../timelineitem/TimelineItemData'
import type { TextStyleConfig } from '../../types'
import { DEFAULT_TEXT_STYLE } from '../../types'

/**
 * 统一文本时间轴工具函数
 * 适配新架构的统一类型系统，提供文本项目的创建、管理和操作功能
 */

/**
 * 生成统一时间轴项目ID
 * 使用新架构的ID生成逻辑
 * @returns 唯一的时间轴项目ID
 */
export function generateUnifiedTimelineItemId(): string {
  return generateUUID4()
}

/**
 * 创建默认变换配置
 * @param x X坐标
 * @param y Y坐标
 * @param width 宽度
 * @param height 高度
 * @returns 变换配置
 */
function createDefaultTransform(
  x: number = 0,
  y: number = 0,
  width: number = 200,
  height: number = 50
): TransformData {
  return {
    x,
    y,
    width,
    height,
    rotation: 0,
    opacity: 1,
    zIndex: 1,
  }
}

/**
 * 创建统一文本时间轴项目
 * 适配新架构的UnifiedTimelineItemData类型
 * @param text 文本内容
 * @param style 文本样式配置（部分）
 * @param startTimeFrames 开始时间（帧数）
 * @param trackId 轨道ID
 * @param duration 显示时长（帧数），默认150帧（5秒@30fps）
 * @param videoResolution 视频分辨率配置
 * @returns Promise<UnifiedTimelineItemData>
 */
export async function createUnifiedTextTimelineItem(
  text: string,
  style: Partial<TextStyleConfig>,
  startTimeFrames: number,
  trackId: string,
  duration: number = TextVisibleSprite.DEFAULT_DURATION,
  videoResolution: { width: number; height: number }
): Promise<UnifiedTimelineItemData> {
  console.log('🔄 [UnifiedTextTimelineUtils] 开始创建统一文本时间轴项目:', {
    text: text.substring(0, 20) + '...',
    startTimeFrames,
    trackId,
    duration,
    videoResolution
  })

  try {
    // 1. 验证和补全文本样式
    const completeStyle = {
      ...DEFAULT_TEXT_STYLE,
      ...style
    }

    // 2. 创建文本精灵（复用现有TextVisibleSprite）
    const textSprite = await TextVisibleSprite.create(text, completeStyle)
    console.log('✅ [UnifiedTextTimelineUtils] 文本精灵创建成功')

    // 3. 设置时间范围
    textSprite.setTimelineStartTime(startTimeFrames)
    textSprite.setDisplayDuration(duration)
    console.log('✅ [UnifiedTextTimelineUtils] 时间范围设置完成:', {
      startTime: startTimeFrames,
      duration: duration,
      endTime: startTimeFrames + duration
    })

    // 4. 设置默认位置（画布中心）
    const canvasWidth = videoResolution.width
    const canvasHeight = videoResolution.height
    textSprite.rect.x = (canvasWidth - textSprite.rect.w) / 2
    textSprite.rect.y = (canvasHeight - textSprite.rect.h) / 2
    console.log('✅ [UnifiedTextTimelineUtils] 默认位置设置完成:', {
      webavX: textSprite.rect.x,
      webavY: textSprite.rect.y,
      width: textSprite.rect.w,
      height: textSprite.rect.h
    })

    // 5. 坐标系转换（WebAV -> 项目坐标系）
    const projectCoords = webavToProjectCoords(
      textSprite.rect.x,
      textSprite.rect.y,
      textSprite.rect.w,
      textSprite.rect.h,
      canvasWidth,
      canvasHeight
    )
    console.log('✅ [UnifiedTextTimelineUtils] 坐标转换完成:', {
      webav: { x: textSprite.rect.x, y: textSprite.rect.y },
      project: projectCoords
    })

    // 6. 创建变换配置
    const transform = createDefaultTransform(
      Math.round(projectCoords.x),
      Math.round(projectCoords.y),
      textSprite.rect.w,
      textSprite.rect.h
    )

    // 7. 创建基础时间轴配置（适配新架构）
    const config: BasicTimelineConfig = {
      name: `文本: ${text.substring(0, 10)}${text.length > 10 ? '...' : ''}`,
      transform,
      // 文本特定配置 - 扩展BasicTimelineConfig以支持文本属性
      ...(text && completeStyle && {
        textConfig: {
          text,
          style: completeStyle,
        }
      })
    }

    // 8. 创建统一时间轴项目（适配新架构）
    const timelineItem: UnifiedTimelineItemData = reactive({
      id: generateUnifiedTimelineItemId(),
      mediaItemId: '', // 文本项目不需要媒体库项目
      trackId,
      timelineStatus: 'ready',
      mediaType: 'text',
      timeRange: textSprite.getTimeRange(), // 使用旧架构的BaseTimeRange，兼容customvisiblesprite
      config,
      sprite: markRaw(textSprite), // 直接持有Sprite引用，与时间轴项目生命周期一致
    })

    console.log('✅ [UnifiedTextTimelineUtils] 统一文本时间轴项目创建完成:', {
      id: timelineItem.id,
      text: text.substring(0, 20) + '...',
      timeRange: timelineItem.timeRange,
      config: {
        name: config.name,
        transform: config.transform,
        textConfig: (config as any).textConfig
      }
    })

    return timelineItem
  } catch (error) {
    console.error('❌ [UnifiedTextTimelineUtils] 创建统一文本时间轴项目失败:', error)
    throw new Error(`创建统一文本项目失败: ${(error as Error).message}`)
  }
}

/**
 * 验证文本轨道兼容性
 * @param trackType 轨道类型
 * @returns 是否兼容文本项目
 */
export function isUnifiedTextTrackCompatible(trackType: string): boolean {
  return trackType === 'text'
}

/**
 * 创建默认文本样式
 * @param overrides 样式覆盖选项
 * @returns 完整的文本样式配置
 */
export function createUnifiedDefaultTextStyle(overrides: Partial<TextStyleConfig> = {}): TextStyleConfig {
  return {
    ...DEFAULT_TEXT_STYLE,
    ...overrides
  }
}

/**
 * 获取统一文本项目的显示名称
 * @param textItem 统一文本时间轴项目
 * @param maxLength 最大显示长度
 * @returns 显示名称
 */
export function getUnifiedTextItemDisplayName(textItem: UnifiedTimelineItemData, maxLength: number = 20): string {
  // 从config中获取文本内容
  const textConfig = (textItem.config as any).textConfig
  const text = textConfig?.text || textItem.config.name || '文本'
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

/**
 * 检查文本内容是否有效
 * @param text 文本内容
 * @returns 是否有效
 */
export function isValidUnifiedTextContent(text: string): boolean {
  return typeof text === 'string' && text.trim().length > 0
}

/**
 * 创建统一文本项目的预览信息
 * @param textItem 统一文本时间轴项目
 * @returns 预览信息对象
 */
export function createUnifiedTextItemPreview(textItem: UnifiedTimelineItemData) {
  const textConfig = (textItem.config as any).textConfig
  const transform = textItem.config.transform
  
  return {
    id: textItem.id,
    text: getUnifiedTextItemDisplayName(textItem),
    style: {
      fontSize: textConfig?.style?.fontSize || DEFAULT_TEXT_STYLE.fontSize,
      fontFamily: textConfig?.style?.fontFamily || DEFAULT_TEXT_STYLE.fontFamily,
      color: textConfig?.style?.color || DEFAULT_TEXT_STYLE.color
    },
    duration: textItem.timeRange.timelineEndTime - textItem.timeRange.timelineStartTime,
    position: {
      x: transform?.x || 0,
      y: transform?.y || 0
    }
  }
}

/**
 * 更新统一文本项目的文本内容
 * @param textItem 统一文本时间轴项目
 * @param newText 新的文本内容
 * @returns 是否需要重新创建sprite
 */
export function updateUnifiedTextItemContent(textItem: UnifiedTimelineItemData, newText: string): boolean {
  const textConfig = (textItem.config as any).textConfig
  if (!textConfig) {
    console.warn('⚠️ [UnifiedTextTimelineUtils] 文本配置不存在，无法更新文本内容')
    return false
  }

  const oldText = textConfig.text
  if (oldText === newText) {
    console.log('📝 [UnifiedTextTimelineUtils] 文本内容未变化，跳过更新')
    return false
  }

  // 更新配置中的文本内容
  textConfig.text = newText
  
  // 更新显示名称
  textItem.config.name = `文本: ${newText.substring(0, 10)}${newText.length > 10 ? '...' : ''}`

  console.log('📝 [UnifiedTextTimelineUtils] 文本内容已更新:', {
    id: textItem.id,
    oldText: oldText.substring(0, 20) + '...',
    newText: newText.substring(0, 20) + '...'
  })

  return true // 需要重新创建sprite
}

/**
 * 更新统一文本项目的样式
 * @param textItem 统一文本时间轴项目
 * @param newStyle 新的样式配置
 * @returns 是否需要重新创建sprite
 */
export function updateUnifiedTextItemStyle(textItem: UnifiedTimelineItemData, newStyle: Partial<TextStyleConfig>): boolean {
  const textConfig = (textItem.config as any).textConfig
  if (!textConfig) {
    console.warn('⚠️ [UnifiedTextTimelineUtils] 文本配置不存在，无法更新样式')
    return false
  }

  const oldStyle = textConfig.style
  const mergedStyle = { ...oldStyle, ...newStyle }
  
  // 检查样式是否真的有变化
  if (JSON.stringify(oldStyle) === JSON.stringify(mergedStyle)) {
    console.log('🎨 [UnifiedTextTimelineUtils] 文本样式未变化，跳过更新')
    return false
  }

  // 更新配置中的样式
  textConfig.style = mergedStyle

  console.log('🎨 [UnifiedTextTimelineUtils] 文本样式已更新:', {
    id: textItem.id,
    oldStyle,
    newStyle: mergedStyle
  })

  return true // 需要重新创建sprite
}
