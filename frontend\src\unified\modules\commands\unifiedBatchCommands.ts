import { generateCommandId } from '../../../utils/idGenerator'
import type { SimpleCommand } from '../../../types'
import type { UnifiedTimelineItemData } from '../../timelineitem/TimelineItemData'
import type { UnifiedMediaItemData } from '../../mediaitem/types'

/**
 * 统一批量命令基类
 * 支持将多个单个命令组合为一个批量操作，统一执行和撤销
 * 适配新架构的统一类型系统
 */
export abstract class UnifiedBaseBatchCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string
  protected subCommands: SimpleCommand[] = []

  constructor(description: string) {
    this.id = generateCommandId()
    this.description = description
  }

  /**
   * 批量执行：依次执行所有子命令
   */
  async execute(): Promise<void> {
    for (const command of this.subCommands) {
      await command.execute()
    }
  }

  /**
   * 批量撤销：逆序撤销所有子命令
   */
  async undo(): Promise<void> {
    for (let i = this.subCommands.length - 1; i >= 0; i--) {
      await this.subCommands[i].undo()
    }
  }

  /**
   * 添加子命令
   */
  protected addCommand(command: SimpleCommand): void {
    this.subCommands.push(command)
  }

  /**
   * 获取批量操作摘要
   */
  getBatchSummary() {
    return {
      totalCommands: this.subCommands.length,
      commands: this.subCommands.map((cmd) => ({
        id: cmd.id,
        description: cmd.description,
      })),
    }
  }
}

/**
 * 统一通用批量命令实现
 */
export class UnifiedGenericBatchCommand extends UnifiedBaseBatchCommand {
  constructor(description: string, commands: SimpleCommand[]) {
    super(description)
    this.subCommands = [...commands]
  }
}

/**
 * 统一批量删除时间轴项目命令
 * 将多个删除操作组合为一个批量操作，统一撤销/重做
 * 适配新架构的统一时间轴项目系统
 */
export class UnifiedBatchDeleteCommand extends UnifiedBaseBatchCommand {
  constructor(
    private timelineItemIds: string[],
    private timelineModule: {
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
      addTimelineItem: (item: UnifiedTimelineItemData) => void
      removeTimelineItem: (id: string) => void
    },
    private mediaModule: {
      getMediaItem: (id: string) => UnifiedMediaItemData | undefined
    },
    // 注意：新架构中可能不需要webavModule，因为sprite管理已经集成到timelineModule中
    private webavModule?: {
      addSprite: (sprite: any) => Promise<boolean>
      removeSprite: (sprite: any) => boolean
    },
  ) {
    super(`批量删除 ${timelineItemIds.length} 个时间轴项目`)
    this.buildDeleteCommands()
  }

  /**
   * 构建删除命令列表
   */
  private buildDeleteCommands() {
    // 动态导入删除命令类（避免循环依赖）
    import('./unifiedTimelineCommands').then(({ UnifiedRemoveTimelineItemCommand }) => {
      for (const itemId of this.timelineItemIds) {
        const item = this.timelineModule.getTimelineItem(itemId)
        if (item) {
          const deleteCommand = new UnifiedRemoveTimelineItemCommand(
            itemId,
            item,
            this.timelineModule,
            this.mediaModule,
            this.webavModule,
          )
          this.addCommand(deleteCommand)
        }
      }

      console.log(`📋 准备批量删除 ${this.subCommands.length} 个时间轴项目`)
    })
  }
}

/**
 * 统一批量自动排列轨道命令
 * 将自动排列操作分解为多个移动命令，支持统一撤销/重做
 * 适配新架构的统一轨道和时间轴系统
 */
export class UnifiedBatchAutoArrangeTrackCommand extends UnifiedBaseBatchCommand {
  constructor(
    private trackId: string,
    private timelineItems: UnifiedTimelineItemData[],
    private timelineModule: {
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
      updateTimelineItemPosition: (id: string, position: number, trackId?: string) => void
    },
    private mediaModule: {
      getMediaItem: (id: string) => UnifiedMediaItemData | undefined
    },
    private trackModule: {
      getTrack: (trackId: string) => any | undefined // 使用any暂时兼容，后续可以定义具体的轨道类型
    },
  ) {
    const track = trackModule.getTrack(trackId)
    super(`自动排列轨道: ${track?.name || `轨道 ${trackId}`}`)
    this.buildMoveCommands()
  }

  /**
   * 构建移动命令列表
   */
  private buildMoveCommands() {
    if (this.timelineItems.length === 0) {
      console.log(`⚠️ 轨道 ${this.trackId} 没有片段需要整理`)
      return
    }

    // 按时间轴开始时间排序
    const sortedItems = [...this.timelineItems].sort((a, b) => {
      const rangeA = a.timeRange
      const rangeB = b.timeRange
      return rangeA.timelineStartTime - rangeB.timelineStartTime
    })

    let currentPositionFrames = 0

    // 动态导入移动命令类（避免循环依赖）
    import('./unifiedTimelineCommands').then(({ UnifiedMoveTimelineItemCommand }) => {
      for (const item of sortedItems) {
        const timeRange = item.timeRange
        // 使用帧数进行所有计算
        const durationFrames = timeRange.timelineEndTime - timeRange.timelineStartTime // 帧数

        // 检查是否需要移动（避免创建无意义的命令）
        const positionChanged =
          Math.abs(timeRange.timelineStartTime - currentPositionFrames) > 1 // 1帧误差容忍

        if (positionChanged) {
          const moveCommand = new UnifiedMoveTimelineItemCommand(
            item.id,
            timeRange.timelineStartTime, // 原始位置（帧数）
            currentPositionFrames, // 新位置（帧数）
            this.trackId, // 轨道不变
            this.trackId,
            this.timelineModule,
            this.mediaModule,
          )
          this.addCommand(moveCommand)
        }

        currentPositionFrames += durationFrames
      }

      const track = this.trackModule.getTrack(this.trackId)
      console.log(
        `📋 准备自动排列轨道: ${track?.name || `轨道 ${this.trackId}`}, 需要移动 ${this.subCommands.length} 个项目`,
      )
    })
  }
}

/**
 * 统一批量属性修改命令
 * 将多个属性修改操作组合为一个批量操作
 * 适配新架构的统一属性系统
 */
export class UnifiedBatchUpdatePropertiesCommand extends UnifiedBaseBatchCommand {
  constructor(targetItemIds: string[], updateCommands: SimpleCommand[]) {
    super(`批量修改 ${targetItemIds.length} 个项目的属性`)

    // 添加所有更新命令
    updateCommands.forEach((command) => this.addCommand(command))

    console.log(`📋 准备批量修改 ${this.subCommands.length} 个属性`)
  }
}
