import { generateCommandId } from '../../../utils/idGenerator'
import type { SimpleCommand } from '../../../types'
import type { UnifiedTrackType } from '../../track/TrackTypes'
import type { UnifiedTimelineItemData } from '../../timelineitem/TimelineItemData'

/**
 * 统一轨道相关的命令实现
 * 提供轨道的创建、删除、重命名等操作的撤销/重做支持
 * 适配新架构的统一类型系统
 */

// ==================== 添加轨道命令 ====================

/**
 * 统一添加轨道命令
 * 支持添加轨道的撤销/重做操作
 * 适配新架构的统一轨道系统
 */
export class UnifiedAddTrackCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string
  private newTrackId: string = '' // 新创建的轨道ID
  private trackData: any = null // 保存轨道数据

  constructor(
    private trackType: UnifiedTrackType,
    private trackName: string | undefined,
    private position: number | undefined,
    private trackModule: {
      addTrack: (type: UnifiedTrackType, name?: string, position?: number) => any
      removeTrack: (trackId: string) => void
      getTrack: (trackId: string) => any | undefined
    },
  ) {
    this.id = generateCommandId()
    this.description = `添加轨道: ${trackName || `${trackType}轨道`}${position !== undefined ? ` (位置: ${position})` : ''}`
  }

  /**
   * 获取新创建的轨道ID
   */
  get createdTrackId(): string {
    return this.newTrackId
  }

  async execute(): Promise<void> {
    try {
      console.log(`🔄 [UnifiedAddTrackCommand] 执行添加轨道操作...`)

      // 添加轨道
      this.trackData = this.trackModule.addTrack(this.trackType, this.trackName, this.position)
      this.newTrackId = this.trackData.id

      console.log(`✅ [UnifiedAddTrackCommand] 轨道添加成功:`, {
        id: this.newTrackId,
        name: this.trackData.name,
        type: this.trackType,
        position: this.position
      })
    } catch (error) {
      console.error(`❌ [UnifiedAddTrackCommand] 添加轨道失败:`, error)
      throw error
    }
  }

  async undo(): Promise<void> {
    try {
      if (this.newTrackId) {
        console.log(`🔄 [UnifiedAddTrackCommand] 撤销添加轨道操作...`)

        // 删除轨道
        this.trackModule.removeTrack(this.newTrackId)

        console.log(`✅ [UnifiedAddTrackCommand] 轨道删除成功: ${this.newTrackId}`)
      }
    } catch (error) {
      console.error(`❌ [UnifiedAddTrackCommand] 撤销添加轨道失败:`, error)
      throw error
    }
  }
}

// ==================== 删除轨道命令 ====================

/**
 * 统一删除轨道命令
 * 支持删除轨道的撤销/重做操作
 * 适配新架构的统一轨道系统
 */
export class UnifiedRemoveTrackCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string
  private trackData: any = null // 保存被删除的轨道数据
  private affectedTimelineItems: UnifiedTimelineItemData[] = [] // 保存被删除的时间轴项目

  constructor(
    private trackId: string,
    private trackModule: {
      removeTrack: (trackId: string) => void
      addTrack: (type: UnifiedTrackType, name?: string, position?: number) => any
      getTrack: (trackId: string) => any | undefined
      restoreTracks: (tracks: any[]) => void
    },
    private timelineModule: {
      timelineItems: { value: UnifiedTimelineItemData[] }
      removeTimelineItem: (id: string) => void
      addTimelineItem: (item: UnifiedTimelineItemData) => void
    },
  ) {
    this.id = generateCommandId()
    
    // 获取轨道信息
    this.trackData = this.trackModule.getTrack(trackId)
    this.description = `删除轨道: ${this.trackData?.name || trackId}`

    // 保存该轨道上的所有时间轴项目
    this.affectedTimelineItems = this.timelineModule.timelineItems.value.filter(
      item => item.trackId === trackId
    )
  }

  async execute(): Promise<void> {
    try {
      console.log(`🔄 [UnifiedRemoveTrackCommand] 执行删除轨道操作...`)

      // 删除该轨道上的所有时间轴项目
      for (const item of this.affectedTimelineItems) {
        this.timelineModule.removeTimelineItem(item.id)
      }

      // 删除轨道
      this.trackModule.removeTrack(this.trackId)

      console.log(`✅ [UnifiedRemoveTrackCommand] 轨道删除成功:`, {
        trackId: this.trackId,
        affectedItems: this.affectedTimelineItems.length
      })
    } catch (error) {
      console.error(`❌ [UnifiedRemoveTrackCommand] 删除轨道失败:`, error)
      throw error
    }
  }

  async undo(): Promise<void> {
    try {
      console.log(`🔄 [UnifiedRemoveTrackCommand] 撤销删除轨道操作...`)

      // 恢复轨道
      if (this.trackData) {
        const restoredTrack = this.trackModule.addTrack(
          this.trackData.type,
          this.trackData.name,
          undefined // 位置可能需要特殊处理
        )
        
        // 更新轨道属性
        Object.assign(restoredTrack, this.trackData)
      }

      // 恢复时间轴项目
      for (const item of this.affectedTimelineItems) {
        this.timelineModule.addTimelineItem(item)
      }

      console.log(`✅ [UnifiedRemoveTrackCommand] 轨道恢复成功: ${this.trackId}`)
    } catch (error) {
      console.error(`❌ [UnifiedRemoveTrackCommand] 撤销删除轨道失败:`, error)
      throw error
    }
  }
}

// ==================== 重命名轨道命令 ====================

/**
 * 统一重命名轨道命令
 * 支持重命名轨道的撤销/重做操作
 * 适配新架构的统一轨道系统
 */
export class UnifiedRenameTrackCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string
  private oldName: string = '' // 保存原始名称用于撤销

  constructor(
    private trackId: string,
    private newName: string,
    private trackModule: {
      renameTrack: (trackId: string, newName: string) => void
      getTrack: (trackId: string) => any | undefined
    },
  ) {
    this.id = generateCommandId()
    this.description = `重命名轨道: ${newName}`

    // 获取当前轨道名称用于撤销
    const track = this.trackModule.getTrack(trackId)
    this.oldName = track?.name || ''
  }

  async execute(): Promise<void> {
    try {
      console.log(`🔄 [UnifiedRenameTrackCommand] 执行重命名轨道操作...`)

      // 重命名轨道
      this.trackModule.renameTrack(this.trackId, this.newName)

      console.log(`✅ [UnifiedRenameTrackCommand] 轨道重命名成功:`, {
        trackId: this.trackId,
        oldName: this.oldName,
        newName: this.newName
      })
    } catch (error) {
      console.error(`❌ [UnifiedRenameTrackCommand] 重命名轨道失败:`, error)
      throw error
    }
  }

  async undo(): Promise<void> {
    try {
      console.log(`🔄 [UnifiedRenameTrackCommand] 撤销重命名轨道操作...`)

      // 恢复原始名称
      this.trackModule.renameTrack(this.trackId, this.oldName)

      console.log(`✅ [UnifiedRenameTrackCommand] 轨道名称恢复成功: ${this.trackId}`)
    } catch (error) {
      console.error(`❌ [UnifiedRenameTrackCommand] 撤销重命名轨道失败:`, error)
      throw error
    }
  }
}

// ==================== 切换轨道可见性命令 ====================

/**
 * 统一切换轨道可见性命令
 * 支持切换轨道可见性的撤销/重做操作
 * 适配新架构的统一轨道系统
 */
export class UnifiedToggleTrackVisibilityCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string
  private previousVisibility: boolean = true // 保存切换前的可见性状态

  constructor(
    private trackId: string,
    private trackModule: {
      getTrack: (trackId: string) => any | undefined
      toggleTrackVisibility: (trackId: string) => void
    },
    private timelineModule: {
      timelineItems: { value: UnifiedTimelineItemData[] }
    },
  ) {
    this.id = generateCommandId()
    
    // 获取当前可见性状态
    const track = this.trackModule.getTrack(trackId)
    this.previousVisibility = track?.isVisible ?? true
    this.description = `${this.previousVisibility ? '隐藏' : '显示'}轨道: ${track?.name || trackId}`
  }

  async execute(): Promise<void> {
    try {
      console.log(`🔄 [UnifiedToggleTrackVisibilityCommand] 执行切换轨道可见性操作...`)

      // 切换轨道可见性
      this.trackModule.toggleTrackVisibility(this.trackId)

      console.log(`✅ [UnifiedToggleTrackVisibilityCommand] 轨道可见性切换成功: ${this.trackId}`)
    } catch (error) {
      console.error(`❌ [UnifiedToggleTrackVisibilityCommand] 切换轨道可见性失败:`, error)
      throw error
    }
  }

  async undo(): Promise<void> {
    try {
      console.log(`🔄 [UnifiedToggleTrackVisibilityCommand] 撤销切换轨道可见性操作...`)

      // 再次切换以恢复原状态
      this.trackModule.toggleTrackVisibility(this.trackId)

      console.log(`✅ [UnifiedToggleTrackVisibilityCommand] 轨道可见性恢复成功: ${this.trackId}`)
    } catch (error) {
      console.error(`❌ [UnifiedToggleTrackVisibilityCommand] 撤销切换轨道可见性失败:`, error)
      throw error
    }
  }
}

// ==================== 切换轨道静音命令 ====================

/**
 * 统一切换轨道静音命令
 * 支持切换轨道静音状态的撤销/重做操作
 * 适配新架构的统一轨道系统
 */
export class UnifiedToggleTrackMuteCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string
  private previousMuteState: boolean = false // 保存切换前的静音状态

  constructor(
    private trackId: string,
    private trackModule: {
      getTrack: (trackId: string) => any | undefined
      toggleTrackMute: (trackId: string) => void
    },
    private timelineModule: {
      timelineItems: { value: UnifiedTimelineItemData[] }
    },
  ) {
    this.id = generateCommandId()
    
    // 获取当前静音状态
    const track = this.trackModule.getTrack(trackId)
    this.previousMuteState = track?.isMuted ?? false
    this.description = `${this.previousMuteState ? '取消静音' : '静音'}轨道: ${track?.name || trackId}`
  }

  async execute(): Promise<void> {
    try {
      console.log(`🔄 [UnifiedToggleTrackMuteCommand] 执行切换轨道静音操作...`)

      // 切换轨道静音状态
      this.trackModule.toggleTrackMute(this.trackId)

      console.log(`✅ [UnifiedToggleTrackMuteCommand] 轨道静音状态切换成功: ${this.trackId}`)
    } catch (error) {
      console.error(`❌ [UnifiedToggleTrackMuteCommand] 切换轨道静音状态失败:`, error)
      throw error
    }
  }

  async undo(): Promise<void> {
    try {
      console.log(`🔄 [UnifiedToggleTrackMuteCommand] 撤销切换轨道静音操作...`)

      // 再次切换以恢复原状态
      this.trackModule.toggleTrackMute(this.trackId)

      console.log(`✅ [UnifiedToggleTrackMuteCommand] 轨道静音状态恢复成功: ${this.trackId}`)
    } catch (error) {
      console.error(`❌ [UnifiedToggleTrackMuteCommand] 撤销切换轨道静音状态失败:`, error)
      throw error
    }
  }
}
